package com.challanty.android.kp3.viewModel.helper

import android.app.ProgressDialog.show
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.util.Constants
import com.challanty.android.kp3.viewModel.TileModel
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject
import kotlin.math.max

/**
 * Helper class for implementing GameViewModel UI logic.
 */
@ViewModelScoped
class GameUIHelper @Inject constructor() {

    private val _gameState = MutableStateFlow(GameState())
    val gameState: StateFlow<GameState> = _gameState.asStateFlow()

    fun toggleProgress(show: Boolean? = null) {
        _gameState.update { currentState ->
            currentState.copy(
                showProgress = show ?: !currentState.showProgress,
                showGameBoard = false,
            )
        }
    }

    suspend fun showStartupGame(
        gameState: GameState,
        settings: Settings
    ) {
        initialTilePlacement()
        delay(100) // Short delay to ensure UI is rendered before we update the state again

        updateGameState(gameState)
        finalTilePlacement(settings)
    }

    suspend fun showNewGame(
        gameState: GameState,
        settings: Settings
    ) {
        // Tiles are already in the correct position and rotation,
        // so we can just update the state.
        updateGameState(gameState)
        finalTilePlacement(settings)
    }

    suspend fun showExistingGame(
        newGameState: GameState?,
        settings: Settings
    ) {
        // Note: A new gameState is provided when the game area size changes

        initialTilePlacement()
        delay(100) // Short delay to ensure UI is rendered before we update the state again

        updateGameState(newGameState ?: gameState.value)
        finalTilePlacement(settings)
    }

    fun showWonGame() {
        gameState.value.tileModels.forEach { tile ->
            tile.setIsLocked(false)
            tile.setIsWon(true)
        }

        _gameState.update { currentState ->
            currentState.copy(
                showProgress = false,
                showGameBoard = true,
            )
        }
    }

    suspend fun handleTileSwap(
        tile1: TileModel,
        tile2: TileModel,
        settings: Settings
    ) {
        val offsetDuration = offsetDuration(settings)
        val rotationDuration = rotationDuration(settings)

        // Save the current offset and board position of tile 1
        val savedOffset = tile1.intOffset
        val savedBoardPosition = tile1.boardPosition

        tile1.update(
            boardPosition = tile2.boardPosition,
            offsetX = tile2.intOffset.x,
            offsetY = tile2.intOffset.y,

            offsetDuration = offsetDuration,
            quarterTurnCnt = tile1.quarterTurnCnt,
            rotationDuration = rotationDuration
        )

        tile2.update(
            boardPosition = savedBoardPosition,
            offsetX = savedOffset.x,
            offsetY = savedOffset.y,

            offsetDuration = offsetDuration,
            quarterTurnCnt = tile2.quarterTurnCnt,
            rotationDuration = rotationDuration
        )

        // Pause game setup if we are animating so user is blocked from interacting
        delay((max(offsetDuration, rotationDuration) * .9).toLong())
    }

    suspend fun handleTileRotation(
        tile: TileModel,
        settings: Settings
    ) {
        val offsetDuration = offsetDuration(settings)
        val rotationDuration = rotationDuration(settings)

        tile.update(
            boardPosition = tile.boardPosition,
            offsetX = tile.intOffset.x,
            offsetY = tile.intOffset.y,
            offsetDuration = offsetDuration,
            // Don't mod quarterTurnCnt by 4 because the rotation animation will
            // rotate counterclockwise instead of clockwise when the value goes
            // from 270 to 0.
            quarterTurnCnt = tile.quarterTurnCnt + 1,
            rotationDuration = rotationDuration
        )

        // Pause game setup if we are animating so user is blocked from interacting
        delay((max(offsetDuration, rotationDuration) *.9).toLong())
    }

    fun updateLockCnt(remainingLocks: Int) {
        _gameState.update { currentState ->
            currentState.copy(lockCnt = remainingLocks)
        }
    }

    fun updateGameState(gameState: GameState) {
        _gameState.value = gameState
    }

    private fun initialTilePlacement() {
        // Place tiles at offset (0, 0) without rotation or animation
        gameState.value.tileModels.forEach { tileViewModel ->
            tileViewModel.update(
                // Don't change board position
                boardPosition = tileViewModel.boardPosition,
                offsetX = 0,
                offsetY = 0,
                offsetDuration = 0,
                quarterTurnCnt = 0,
                rotationDuration = 0
            )
        }
    }

    private suspend fun finalTilePlacement(settings: Settings) {
        // Move tiles to their game board positions and rotate one full turn just for show
        val offsetDuration = offsetDuration(settings)
        val rotationDuration = rotationDuration(settings)
        val tilePxSize = gameState.value.tilePxSize

        // First, position all tiles at the upper-left corner (0,0)
        initialTilePlacement()

        // Wait a moment for the UI to render with tiles at the upper-left corner
        delay(500) // Short delay to ensure UI is rendered

        // Now move tiles to their final positions and rotate them
        gameState.value.tileModels.forEach { tile ->

            val boardRow = tile.boardPosition.x
            val boardCol = tile.boardPosition.y

            tile.update(
                boardPosition = tile.boardPosition, // Don't change board position
                offsetX = tilePxSize.width * boardCol,
                offsetY = tilePxSize.height * boardRow,
                offsetDuration = offsetDuration,
                quarterTurnCnt = 4, // Show tile rotation during layout (must end with tile in original position)
                rotationDuration = rotationDuration
            )
        }

        // Pause game setup if we are animating so user is blocked from interacting
        delay((max(offsetDuration, rotationDuration) * .9).toLong())
    }

    private fun offsetDuration(settings: Settings): Int {
        return if (settings.animate && settings.animateSwap) Constants.ANIMATE_SWAP_DURATION else 0
    }

    private fun rotationDuration(settings: Settings): Int {
        return if (settings.animate && settings.animateRotation) Constants.ANIMATE_ROTATION_DURATION else 0
    }
}